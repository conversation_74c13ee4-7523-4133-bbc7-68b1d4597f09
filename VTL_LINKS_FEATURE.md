# VTL Profile Links Feature

## Overview
The VALORANT rank yoinker application now displays <PERSON><PERSON> (VALORANT Tracker League) profile URLs for all players after the main table, making it easy to access player profiles.

## How It Works

### URL Format
Player names are converted from the format `username#tagline` to VTL URLs:
```
https://vtl.lol/id/username_tagline
```

### Examples
- `TestPlayer#1234` → `https://vtl.lol/id/TestPlayer_1234`
- `ProGamer#5678` → `https://vtl.lol/id/ProGamer_5678`
- `ValorantFan#0001` → `https://vtl.lol/id/ValorantFan_0001`

### Display Format
After the main player table, you'll see a section like this:
```
VTL Profile Links:
  TestPlayer#1234: https://vtl.lol/id/TestPlayer_1234
  ProGamer#5678: https://vtl.lol/id/ProGamer_5678
  ValorantFan#0001: https://vtl.lol/id/ValorantFan_0001
```

## Implementation Details

### Files Modified

1. **src/colors.py**
   - Added `create_vtl_url()` method to generate VTL URLs from player names
   - Added `wrap_name_with_link()` method to wrap names with Rich link markup
   - Modified `get_color_from_team()` to apply VTL links to appropriate player names

2. **src/table.py**
   - Updated `ansi_to_console()` method to handle Rich link markup
   - Added `_process_ansi_codes()` helper method for ANSI code processing
   - Enhanced markup processing to preserve both colors and links

3. **main.py**
   - Updated MENUS state to use VTL link functionality for consistency

### Link Application Logic

VTL URLs are generated for:
- ✅ All real player names in standard format (`username#tagline`)
- ✅ Players in all game states (INGAME, PREGAME, MENUS)
- ✅ Both team members and opponents
- ✅ Party members and solo players

VTL URLs are generated for all players regardless of streamer mode settings.

## Technical Implementation

### URL Generation Process
1. Player name retrieved in format `username#tagline`
2. VTL URL generated by replacing `#` with `_`
3. URL stored during table processing
4. URLs displayed after the main table

### Processing Pipeline
1. **Collection**: VTL URLs are collected as player data is processed
2. **Storage**: URLs are temporarily stored in the Colors class
3. **Display**: After the table is shown, URLs are printed in a formatted list
4. **Cleanup**: URLs are cleared for the next update cycle

## Usage

No additional configuration is required. The feature is automatically enabled for all player names that follow the standard VALORANT format (`username#tagline`).

### How to Use VTL Links
1. Run the application as normal
2. After the player table is displayed, scroll down to see "VTL Profile Links:"
3. Copy and paste the URLs into your web browser to view player profiles
4. URLs are updated each time the player data refreshes
