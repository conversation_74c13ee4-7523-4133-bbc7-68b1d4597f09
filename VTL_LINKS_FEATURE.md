# VTL Profile Links Feature

## Overview
Player names in the VALORANT rank yoinker application are now clickable links that direct to their VTL (VALORANT Tracker League) profiles.

## How It Works

### URL Format
Player names are converted from the format `username#tagline` to VTL URLs:
```
https://vtl.lol/id/username_tagline
```

### Examples
- `TestPlayer#1234` → `https://vtl.lol/id/TestPlayer_1234`
- `ProGamer#5678` → `https://vtl.lol/id/ProGamer_5678`
- `ValorantFan#0001` → `https://vtl.lol/id/ValorantFan_0001`

## Implementation Details

### Files Modified

1. **src/colors.py**
   - Added `create_vtl_url()` method to generate VTL URLs from player names
   - Added `wrap_name_with_link()` method to wrap names with Rich link markup
   - Modified `get_color_from_team()` to apply VTL links to appropriate player names

2. **src/table.py**
   - Updated `ansi_to_console()` method to handle Rich link markup
   - Added `_process_ansi_codes()` helper method for ANSI code processing
   - Enhanced markup processing to preserve both colors and links

3. **main.py**
   - Updated MENUS state to use VTL link functionality for consistency

### Link Application Logic

VTL links are applied to:
- ✅ Real player names (not in streamer mode)
- ✅ Party members (always show real names with links)
- ✅ Your own player name
- ✅ Players in all game states (INGAME, PREGAME, MENUS)

VTL links are NOT applied to:
- ❌ Agent names (when streamer mode is active and bypass is disabled)
- ❌ "Player" placeholder names

## Terminal Compatibility

The clickable links work in terminals that support Rich markup, including:
- ✅ Windows Terminal
- ✅ iTerm2 (macOS)
- ✅ Modern terminal emulators with hyperlink support
- ❌ Basic Command Prompt (links appear as text but are not clickable)

## Technical Implementation

### Rich Markup Format
Names are wrapped with Rich link markup:
```
[link=https://vtl.lol/id/username_tagline]colored_name[/link]
```

### Processing Pipeline
1. Player name retrieved in format `username#tagline`
2. VTL URL generated by replacing `#` with `_`
3. Name colored based on team/status
4. Colored name wrapped with Rich link markup
5. ANSI codes converted to Rich markup in table processing
6. Final output rendered with clickable links

## Usage

No additional configuration is required. The feature is automatically enabled for all player names that follow the standard VALORANT format (`username#tagline`).

When you see player names in the application, simply click on them to open their VTL profile in your default web browser.
