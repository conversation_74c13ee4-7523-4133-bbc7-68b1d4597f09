# Streamer Mode Detection and Bypass Features

This update adds new functionality to detect and optionally bypass VALORANT's streamer mode (incognito mode) for player names.

## New Configuration Options

Two new feature flags have been added to the `config.json` file under the `flags` section:

### `show_streamer_names`
- **Default**: `true`
- **Description**: When enabled, shows the real player names even when they have streamer mode (incognito) enabled
- **When disabled**: Shows agent names for players in streamer mode (original behavior)

### `streamer_mode_indicator`
- **Default**: `true`
- **Description**: When enabled, adds a visual `[S]` indicator in orange color next to players who are in streamer mode
- **When disabled**: No visual indication of streamer mode status

## How It Works

### Detection
The application automatically detects when players have streamer mode enabled by checking the `PlayerIdentity.Incognito` flag from the VALORANT API.

### Name Display Logic
1. **Normal players** (not in streamer mode): Always show real names
2. **Streamer mode players** with `show_streamer_names: true`: Show real names with optional `[S]` indicator
3. **Streamer mode players** with `show_streamer_names: false`: Show agent names (e.g., "<PERSON>", "<PERSON>")

### Visual Indicators
When `streamer_mode_indicator` is enabled, players in streamer mode will have an orange `[S]` prefix before their name to clearly identify them.

## Configuration Examples

### Enable both features (recommended):
```json
{
  "flags": {
    "show_streamer_names": true,
    "streamer_mode_indicator": true
  }
}
```

### Show real names but no indicator:
```json
{
  "flags": {
    "show_streamer_names": true,
    "streamer_mode_indicator": false
  }
}
```

### Respect streamer mode (original behavior):
```json
{
  "flags": {
    "show_streamer_names": false,
    "streamer_mode_indicator": false
  }
}
```

## Privacy Considerations

- The application can only show real names because it has access to the VALORANT API through your authenticated session
- This feature works in both competitive and casual game modes
- The streamer mode detection works in both agent select (pregame) and in-game phases
- Players in your party are always shown with their real names regardless of these settings

## Technical Notes

- These features work by conditionally passing the agent parameter to the name coloring function
- The real player names are always fetched from the VALORANT name service API
- The visual indicator uses ANSI color codes for terminal display
- Changes to these settings require restarting the application to take effect
