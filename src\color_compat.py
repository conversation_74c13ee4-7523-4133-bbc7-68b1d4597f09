"""
Compatibility module to replace colr functionality with colorama for Python 3.13+ compatibility
"""

import colorama
from colorama import Fore, Back, Style

# Initialize colorama
colorama.init(autoreset=True)

def color(text, fore=None, back=None, style=None):
    """
    Color function to replace colr.color functionality with proper ANSI RGB codes

    Args:
        text: Text to colorize
        fore: Foreground color as RGB tuple (r, g, b) or None
        back: Background color as RGB tuple (r, g, b) or None
        style: Style (not used in this simple implementation)

    Returns:
        Colored text string with proper ANSI RGB codes
    """
    if fore is None:
        return str(text)

    # Use proper ANSI RGB escape codes that the table parser expects
    r, g, b = fore

    # Format: \x1b[38;2;r;g;bm for foreground RGB
    return f"\x1b[38;2;{r};{g};{b}m{text}\x1b[0m"
